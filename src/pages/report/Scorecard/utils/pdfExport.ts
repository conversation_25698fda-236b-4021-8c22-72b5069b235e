import {
  FinancialMetric,
  OccupancyMetric,
  PerformanceRow,
  PropertyInfo,
  RentalMetric,
  ReputationMetric,
  SummaryText,
} from '@/slice/scoreCardSlice';
import jsPDF from 'jspdf';
import { ScoreCardData } from '@/types/scorecardTypes';
import { FilterValues } from '../components/filters';

interface ScorecardPDFData {
  propertyInfo: PropertyInfo;
  occupancyMetrics: OccupancyMetric[];
  rentalMetrics: RentalMetric[];
  financialMetrics: FinancialMetric[];
  performanceRows: PerformanceRow[];
  reputationMetrics: ReputationMetric[];
  summaryTexts: SummaryText[];
  ytdTurnCost: string;
  financialDateRange: string;
  filters: FilterValues;
  rawApiData: ScoreCardData | null;
  screenshotDataUrl: string; // Add screenshot data
}

const getImageFormatFromBase64 = (base64String: string): string => {
  let format = 'JPEG';

  if (base64String.startsWith('data:')) {
    const mimeMatch = base64String.match(/^data:image\/(\w+);base64,/);
    if (mimeMatch && mimeMatch[1]) {
      const mimeType = mimeMatch[1].toLowerCase();
      switch (mimeType) {
        case 'png':
          format = 'PNG';
          break;
        case 'jpeg':
        case 'jpg':
          format = 'JPEG';
          break;
        case 'gif':
          format = 'GIF';
          break;
        case 'webp':
          format = 'WEBP';
          break;
        default:
          format = 'JPEG';
      }
    }
  }

  return format;
};

// PDF generation using full-page screenshot including sidebar
const generateFullPagePDFFromScreenshot = (
  doc: jsPDF,
  screenshotDataUrl: string,
): void => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();

  // Add full-page screenshot that includes the sidebar
  try {
    const screenshotFormat = getImageFormatFromBase64(screenshotDataUrl);
    doc.addImage(
      screenshotDataUrl,
      screenshotFormat,
      10,
      10,
      pageWidth,
      pageHeight,
      undefined,
      'FAST',
    );
  } catch (error) {
    console.error('Error adding screenshot to PDF:', error);

    // Fallback: Add error message
    doc.setFillColor(255, 255, 255);
    doc.rect(10, 10, pageWidth - 20, pageHeight - 20, 'F');
    doc.setTextColor(255, 0, 0);
    doc.setFont('Helvetica', 'normal');
    doc.setFontSize(16);
    doc.text(
      'Error: Unable to render content screenshot',
      pageWidth / 2,
      pageHeight / 2 - 20,
      { align: 'center' },
    );
    doc.setFontSize(12);
    doc.text(
      'Please try exporting again or contact support if the issue persists.',
      pageWidth / 2,
      pageHeight / 2 + 10,
      { align: 'center' },
    );
  }
};

export const exportScorecardToPDF = async (data: ScorecardPDFData) => {
  try {
    const doc = new jsPDF('l', 'pt', 'a4'); // Landscape A4

    // Generate PDF using full-page screenshot including sidebar
    generateFullPagePDFFromScreenshot(doc, data.screenshotDataUrl);

    // File naming and save
    const propertyName = data.propertyInfo.propertyName.replace(
      /[^a-zA-Z0-9]/g,
      '_',
    );
    const dateRange = data.filters.reportingPeriod.replace(
      /[^a-zA-Z0-9]/g,
      '_',
    );
    const fileName = `Scorecard_${propertyName}_${dateRange}.pdf`;

    doc.save(fileName);
  } catch (error) {
    console.error('PDF export failed:', error);
    throw error; // Re-throw to be handled by the hook
  }
};
