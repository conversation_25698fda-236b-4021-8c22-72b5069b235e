import { HTMLAttributes, ReactNode } from 'react';
import Styles from './CommonReportsTable.module.css';

const mergeHeadingCellPadding = 'p-3';
const headingCellPadding = 'py-3 px-2';
const bodyCellPadding = 'px-2';

type PropsType = {
  children: ReactNode;
  className?: string;
} & HTMLAttributes<HTMLTableElement>;

type RowPropsType = {
  children: ReactNode;
  className?: string;
} & HTMLAttributes<HTMLTableRowElement>;

interface BodyRowPropsTypes extends RowPropsType {
  borderBtmFirstColum?: boolean;
  lastRowBorder?: boolean;
}

type MergeCellProps = {
  children: ReactNode;
  colSpan?: number;
  className?: string;
} & HTMLAttributes<HTMLTableCellElement>;

type CellPropsTypes = {
  children: ReactNode;
  borderRight?: boolean;
  borderLeft?: boolean;
  className?: string;
  subHeading?: boolean;
  textAlign?: 'text-start' | 'text-end' | 'text-center';
} & HTMLAttributes<HTMLTableCellElement>;

const CommonTable = ({ children, className, ...rest }: PropsType) => (
  <table
    style={{
      wordWrap: 'break-word',
      textWrap: 'wrap',
      overflowWrap: 'anywhere',
    }}
    className={`${Styles.table1} ${className ?? ''}`}
    {...rest}
  >
    {children}
  </table>
);

const CommonTableMainHeaderRow = ({
  children,
  className = '',
  height,
}: RowPropsType & { height?: string }) => {
  return <tr className={`${height} ${className}`}>{children}</tr>;
};

const CommonTableSubHeaderRow = ({ children, className }: RowPropsType) => {
  return <tr className={` ${className} `}>{children}</tr>;
};

const CommonTableBodyRow = ({
  children,
  borderBtmFirstColum = false,
  lastRowBorder = true,
  // ...rest
}: BodyRowPropsTypes) => {
  return (
    <tr
      className={`
        ${Styles.bodyRow} 
      ${lastRowBorder ? Styles.lastRowBorderBtm : ''} 
      ${lastRowBorder && borderBtmFirstColum ? Styles.lastRowFirstCellBorderBtm : ''}
     
      `}
      // {...rest}
    >
      {children}
    </tr>
  );
};

const CommonTableBodyRowTotal = ({
  children,
  className,
  borderBtmFirstColum = false,
  lastRowBorder = true,
}: BodyRowPropsTypes) => {
  return (
    <tr
      className={`bg-[#DEEFFF] text-[#43298F] 
        ${className} 
       ${lastRowBorder ? Styles.lastRowBorderBtm : ''} 
      ${lastRowBorder && borderBtmFirstColum ? Styles.lastRowFirstCellBorderBtm : ''}
        `}
    >
      {children}
    </tr>
  );
};

const CommonTableHeadingMergeCell = ({
  colSpan,
  children,
  className,
  bg = 'bg-white',
  fontSize = 'text-sm',
  fontWeight = 'font-medium',
  uppercase = true,
  tracking = 'tracking-wider',
  padding = mergeHeadingCellPadding,
}: MergeCellProps & {
  bg?: string;
  fontSize?: string;
  fontWeight?: string;
  uppercase?: boolean;
  tracking?: string;
  padding?: string;
}) => {
  return (
    <th
      colSpan={colSpan}
      className={`
        ${Styles.headingMergeCell} 
        ${bg} 
        ${fontSize} ${fontWeight}
        ${uppercase ? 'uppercase' : ''}
        ${tracking} ${padding}
        ${className}`}
    >
      {children}
    </th>
  );
};

const CommonTableHeadingCell = ({
  children,
  borderRight,
  borderLeft,
  subHeading = true,
  textAlign = 'text-end',
  className,
  fontSize = 'text-xs',
  fontWeight = 'font-medium',
  uppercase = true,
  tracking = 'tracking-wider',
  padding = headingCellPadding,
  colSpan,
}: CellPropsTypes & {
  fontSize?: string;
  fontWeight?: string;
  uppercase?: boolean;
  tracking?: string;
  padding?: string;
  colSpan?: number;
}) => {
  return (
    <th
      colSpan={colSpan}
      className={` 
        ${subHeading ? Styles.subHeadingCell : ''}
      ${borderRight ? Styles.borderRight : ''} 
      ${borderLeft ? Styles.borderLeft : ''} 
      ${textAlign}  
      ${fontSize}
      ${fontWeight}
      ${uppercase ? 'uppercase' : ''}
      ${tracking} 
      ${padding}
      ${className}
     
      `}
    >
      {children}
    </th>
  );
};

const CommonTableBodyCell = ({
  children,
  borderRight,
  borderLeft,
  className,
  textAlign = 'text-end',
  padding = bodyCellPadding,
}: CellPropsTypes & { padding?: string }) => {
  return (
    <td
      className={`
        ${Styles.bodyCell} 
        ${textAlign} 
        ${borderRight ? Styles.borderRight : ''} 
        ${borderLeft ? Styles.borderLeft : ''}
        ${padding}
        ${className}
          border-r border-[#DEEFFF] last:border-r-0
      `}
    >
      {children}
    </td>
  );
};

const CommonTableBodyTotalCell = ({
  children,
  borderRight,
  borderLeft,
  className,
  textAlign = 'text-end',
  padding = bodyCellPadding,
}: CellPropsTypes & { padding?: string }) => {
  return (
    <td
      className={`bg-[#DEEFFF]' font-bold ${textAlign} ${className}
        ${padding}
      ${borderRight ? Styles.borderRight : ''} 
      ${borderLeft ? Styles.borderLeft : ''}`}
    >
      {children}
    </td>
  );
};

const CommonTableColumnSeperateCell = () => {
  return (
    <td
      // className="bg-[#f4f4ff] w-10"
      // className="bg-[#f4f4ff] min-w-10 max-w-10"
      className="bg-white min-w-10 max-w-10 border-0"
      style={{
        // border: 'none',
        borderBottom: '1px solid #fff',
        // border: "1px solid #f4f4ff",
        // background: "#f4f4ff",
        // border: "1px solid #f4f4ff",
        // borderColor:"#f4f4ff",
        // borderColor:"red",
      }}
    ></td>
  );
};

export {
  CommonTable,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
  CommonTableBodyRowTotal,
  CommonTableHeadingMergeCell,
  CommonTableHeadingCell,
  CommonTableBodyRow,
  CommonTableBodyCell,
  CommonTableBodyTotalCell,
  CommonTableColumnSeperateCell,
};
