import { isAdmin, isDeveloper, isScorecardReader } from '@/constants/constants';

type RoutePermission = {
  path: string;
  allowedRoles: ((userRoles: string[] | null) => boolean)[];
};

export const routePermissions: RoutePermission[] = [
  {
    path: '/pipeline',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/statement-of-operations-ytd',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/statement-of-operations-ptd',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/statement-of-operations-by-thousand',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/managed-units-wa-units-report',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/property-performance-scorecard',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/scorecard',
    allowedRoles: [isAdmin, isDeveloper, isScorecardReader],
  },
  {
    path: '/financial-kpi',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/unit-walk',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/property-management-kpis',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/business-development-kpis',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/staffing-kpis',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/repository',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/report-scheduler',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/alerts',
    allowedRoles: [isAdmin, isDeveloper],
  },
  {
    path: '/performance-scorecard-dictionary',
    allowedRoles: [isAdmin, isDeveloper, isScorecardReader],
  },
];

export function getRoutePermissions(path: string) {
  return routePermissions.find((route) => route.path === path);
}
